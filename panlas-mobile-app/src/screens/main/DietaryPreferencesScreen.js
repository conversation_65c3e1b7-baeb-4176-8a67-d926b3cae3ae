import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { userAPI, aiAPI, mealPlansAPI } from '../../services/api';
import { useAuth } from '../../context/AuthContext';
import { colors, legacyFonts as fonts, legacySpacing as spacing, borderRadius } from '../../styles/theme';
import { commonStyles } from '../../styles/commonStyles';

const DietaryPreferencesScreen = ({ navigation }) => {
  const [preferences, setPreferences] = useState({
    restrictions: [],
    allergies: [],
    dislikedIngredients: [],
    calorieTarget: '',
    mealFrequency: 3
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [conflicts, setConflicts] = useState(null);
  const [checkingConflicts, setCheckingConflicts] = useState(false);
  const [familyMembers, setFamilyMembers] = useState([]);
  const [customAllergy, setCustomAllergy] = useState('');
  const [showCustomAllergyInput, setShowCustomAllergyInput] = useState(false);
  const [customRestriction, setCustomRestriction] = useState('');
  const [showCustomRestrictionInput, setShowCustomRestrictionInput] = useState(false);
  const [mealPlanUpdateResult, setMealPlanUpdateResult] = useState(null);
  const [showMealPlanUpdate, setShowMealPlanUpdate] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [savingStatus, setSavingStatus] = useState('');

  const { user } = useAuth();

  // Dietary restrictions options (matching database dietType fields)
  const dietaryOptions = [
    'Vegetarian',
    'Vegan',
    'Gluten-Free',
    'Dairy-Free',
    'Nut-Free',
    'Low-Carb',
    'Keto',
    'Pescatarian',
    'Halal'
  ];

  // Allergy options (matching database allergens field)
  const allergyOptions = [
    'Milk',
    'Eggs',
    'Fish',
    'Shellfish',
    'Peanuts',
    'Tree Nuts',
    'Wheat',
    'Soybeans',
    'Sesame',
    'Mustard',
    'Celery',
    'Lupin',
    'Mollusks',
    'Sulfites'
  ];

  // Migration mapping for old allergy names to new standardized names
  const allergyMigrationMap = {
    'Nuts': 'Tree Nuts',
    'Gluten': 'Wheat',
    'Soy': 'Soybeans',
    'Dairy': 'Milk'
  };

  // Function to migrate old allergy names to new ones
  const migrateAllergies = (allergies) => {
    return allergies.map(allergy => allergyMigrationMap[allergy] || allergy);
  };

  // Check if there are custom allergies and set the "Other" option accordingly
  useEffect(() => {
    const customAllergies = preferences.allergies.filter(allergy => !allergyOptions.includes(allergy));
    if (customAllergies.length > 0) {
      setShowCustomAllergyInput(true);
      setCustomAllergy(customAllergies.join(', ')); // Show all custom allergies in the text field
    }
  }, [preferences.allergies]);

  // Check if there are custom restrictions and set the "Other" option accordingly
  useEffect(() => {
    const customRestrictions = preferences.restrictions.filter(restriction => !dietaryOptions.includes(restriction));
    if (customRestrictions.length > 0) {
      setShowCustomRestrictionInput(true);
      setCustomRestriction(customRestrictions.join(', ')); // Show all custom restrictions in the text field
    }
  }, [preferences.restrictions]);

  useEffect(() => {
    loadPreferences();
    loadFamilyMembers();
  }, []);

  const loadFamilyMembers = async () => {
    try {
      const response = await userAPI.getFamilyMembers();
      setFamilyMembers(response.data || []);
    } catch (error) {
      console.error('Error loading family members:', error);
      setFamilyMembers([]);
    }
  };

  const loadPreferences = async () => {
    try {
      setLoading(true);
      const response = await userAPI.getDietaryPreferences();
      console.log('Loaded preferences:', response);
      
      if (response.success && response.dietaryPreferences) {
        // Migrate old allergy names to new standardized names
        const migratedAllergies = migrateAllergies(response.dietaryPreferences.allergies || []);

        setPreferences({
          restrictions: response.dietaryPreferences.restrictions || [],
          allergies: migratedAllergies,
          dislikedIngredients: response.dietaryPreferences.dislikedIngredients || [],
          calorieTarget: response.dietaryPreferences.calorieTarget?.toString() || '',
          mealFrequency: response.dietaryPreferences.mealFrequency || 3
        });
      }
    } catch (error) {
      console.error('Error loading preferences:', error);
      Alert.alert('Error', 'Failed to load dietary preferences');
    } finally {
      setLoading(false);
    }
  };

  const savePreferences = async () => {
    try {
      setSaving(true);
      setIsSaving(true);
      setSavingStatus('Saving dietary preferences...');
      console.log('🔄 Starting save process - isSaving set to true');

      // Check if at least one dietary preference is selected (restrictions or allergies)
      const hasRestrictions = preferences.restrictions.length > 0;
      const hasAllergies = preferences.allergies.length > 0;

      if (!hasRestrictions && !hasAllergies) {
        Alert.alert(
          'Dietary Preferences Required',
          'Please select at least one dietary restriction or allergy. This helps us provide better meal recommendations.',
          [{ text: 'OK' }]
        );
        setSaving(false);
        setIsSaving(false);
        setSavingStatus('');
        return;
      }

      const preferencesToSave = {
        restrictions: preferences.restrictions,
        allergies: preferences.allergies,
        dislikedIngredients: preferences.dislikedIngredients,
        calorieTarget: preferences.calorieTarget ? parseInt(preferences.calorieTarget) : null,
        mealFrequency: preferences.mealFrequency
      };

      console.log('Saving preferences:', preferencesToSave);

      // Update status to show AI is working
      setSavingStatus('AI is reviewing your meal plans...');

      const response = await userAPI.updateDietaryPreferences(preferencesToSave);
      console.log('Save response:', response);

      if (response.success) {
        // Update status to show completion
        setSavingStatus('Dietary preferences saved successfully!');

        // Check if meal plans were updated automatically
        if (response.mealPlanUpdates && response.mealPlanUpdates.success) {
          const updateInfo = response.mealPlanUpdates;
          setMealPlanUpdateResult(updateInfo);
          setShowMealPlanUpdate(true);

          // Show detailed alert about meal plan updates
          const message = updateInfo.plansUpdated > 0
            ? `Your dietary preferences have been saved!\n\n🍽️ Meal Plan Updates:\n• ${updateInfo.plansUpdated} meal plans updated\n• ${updateInfo.mealsReplaced} meals replaced with AI suggestions\n\nYour future meal plans now match your new dietary preferences!`
            : 'Your dietary preferences have been saved!\n\nNo changes were needed to your existing meal plans.';

          Alert.alert('Success', message, [
            {
              text: 'View Details',
              onPress: () => {
                // Navigate to meal plans screen to show updates
                navigation.navigate('MealPlans');
              }
            },
            {
              text: 'OK',
              onPress: () => navigation.goBack()
            }
          ]);
        } else {
          // Reload preferences to ensure sync
          await loadPreferences();
          Alert.alert('Success', 'Dietary preferences saved successfully!');
          navigation.goBack();
        }
      } else {
        Alert.alert('Error', 'Failed to save preferences');
      }
    } catch (error) {
      console.error('Error saving preferences:', error);
      Alert.alert('Error', 'Failed to save dietary preferences');
    } finally {
      setSaving(false);
      setIsSaving(false);
      setSavingStatus('');
    }
  };

  const toggleRestriction = (restriction) => {
    setPreferences(prev => ({
      ...prev,
      restrictions: prev.restrictions.includes(restriction)
        ? prev.restrictions.filter(r => r !== restriction)
        : [...prev.restrictions, restriction]
    }));
  };

  const toggleAllergy = (allergy) => {
    setPreferences(prev => ({
      ...prev,
      allergies: prev.allergies.includes(allergy)
        ? prev.allergies.filter(a => a !== allergy)
        : [...prev.allergies, allergy]
    }));
  };

  const addCustomAllergy = () => {
    if (customAllergy.trim()) {
      // Split by comma and add multiple allergies
      const newAllergies = customAllergy.split(',').map(a => a.trim()).filter(a => a && !preferences.allergies.includes(a));
      if (newAllergies.length > 0) {
        setPreferences(prev => ({
          ...prev,
          allergies: [...prev.allergies, ...newAllergies]
        }));
        setCustomAllergy('');
        setShowCustomAllergyInput(false);
      }
    }
  };

  const addCustomRestriction = () => {
    if (customRestriction.trim()) {
      // Split by comma and add multiple restrictions
      const newRestrictions = customRestriction.split(',').map(r => r.trim()).filter(r => r && !preferences.restrictions.includes(r));
      if (newRestrictions.length > 0) {
        setPreferences(prev => ({
          ...prev,
          restrictions: [...prev.restrictions, ...newRestrictions]
        }));
        setCustomRestriction('');
        setShowCustomRestrictionInput(false);
      }
    }
  };

  const removeCustomAllergy = (allergy) => {
    setPreferences(prev => {
      const updatedAllergies = prev.allergies.filter(a => a !== allergy);
      const customAllergies = updatedAllergies.filter(a => !allergyOptions.includes(a));

      // Update the custom allergy text field
      setCustomAllergy(customAllergies.join(', '));

      // Hide the input if no custom allergies remain
      if (customAllergies.length === 0) {
        setShowCustomAllergyInput(false);
      }

      return {
        ...prev,
        allergies: updatedAllergies
      };
    });
  };

  const removeCustomRestriction = (restriction) => {
    setPreferences(prev => {
      const updatedRestrictions = prev.restrictions.filter(r => r !== restriction);
      const customRestrictions = updatedRestrictions.filter(r => !dietaryOptions.includes(r));

      // Update the custom restriction text field
      setCustomRestriction(customRestrictions.join(', '));

      // Hide the input if no custom restrictions remain
      if (customRestrictions.length === 0) {
        setShowCustomRestrictionInput(false);
      }

      return {
        ...prev,
        restrictions: updatedRestrictions
      };
    });
  };

  const checkDietaryConflicts = async (currentPreferences) => {
    try {
      setCheckingConflicts(true);

      // Prepare family member preferences for conflict checking
      const familyPreferences = familyMembers.map(member => ({
        name: member.name,
        restrictions: member.dietaryPreferences?.restrictions || [],
        allergies: member.dietaryPreferences?.allergies || [],
        dislikedIngredients: member.dietaryPreferences?.dislikedIngredients || []
      }));

      const requestData = {
        userPreferences: {
          restrictions: currentPreferences.restrictions,
          allergies: currentPreferences.allergies,
          dislikedIngredients: currentPreferences.dislikedIngredients
        },
        familyMembers: familyPreferences
      };

      const response = await aiAPI.detectDietaryConflicts(requestData);

      if (response.data.success && response.data.conflicts) {
        setConflicts(response.data.conflicts);
      } else {
        setConflicts(null);
      }
    } catch (error) {
      console.error('Error checking dietary conflicts:', error);
      setConflicts(null);
    } finally {
      setCheckingConflicts(false);
    }
  };

  // Check for conflicts whenever preferences change
  useEffect(() => {
    if (preferences.restrictions.length > 0 || preferences.allergies.length > 0) {
      const timeoutId = setTimeout(() => {
        checkDietaryConflicts(preferences);
      }, 1000); // Debounce for 1 second

      return () => clearTimeout(timeoutId);
    } else {
      setConflicts(null);
    }
  }, [preferences.restrictions, preferences.allergies, preferences.dislikedIngredients, familyMembers]);

  const renderOptionButton = (option, isSelected, onPress, color = colors.primary) => (
    <TouchableOpacity
      key={option}
      style={[
        styles.optionButton,
        isSelected && { backgroundColor: color, borderColor: color }
      ]}
      onPress={onPress}
    >
      <Text style={[
        styles.optionText,
        isSelected && styles.optionTextSelected
      ]}>
        {option}
      </Text>
      {isSelected && (
        <Ionicons name="checkmark" size={16} color={colors.surface} style={styles.checkmark} />
      )}
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <SafeAreaView style={commonStyles.container}>
        <View style={commonStyles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={commonStyles.loadingText}>Loading preferences...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={commonStyles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={colors.surface} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Dietary Preferences</Text>
        <TouchableOpacity onPress={savePreferences} disabled={saving}>
          {saving ? (
            <ActivityIndicator size="small" color={colors.surface} />
          ) : (
            <Text style={styles.saveButton}>Save</Text>
          )}
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.container}>
        {/* Dietary Restrictions Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Dietary Restrictions</Text>
          <Text style={styles.sectionSubtitle}>
            Select all that apply to you
          </Text>
          <View style={styles.optionsContainer}>
            {dietaryOptions.map(option =>
              renderOptionButton(
                option,
                preferences.restrictions.includes(option),
                () => toggleRestriction(option),
                colors.primary
              )
            )}
            {/* Other option button */}
            <TouchableOpacity
              style={[
                styles.optionButton,
                (showCustomRestrictionInput || preferences.restrictions.filter(restriction => !dietaryOptions.includes(restriction)).length > 0) && { backgroundColor: colors.primary, borderColor: colors.primary }
              ]}
              onPress={() => setShowCustomRestrictionInput(!showCustomRestrictionInput)}
            >
              <Text style={[
                styles.optionText,
                (showCustomRestrictionInput || preferences.restrictions.filter(restriction => !dietaryOptions.includes(restriction)).length > 0) && styles.optionTextSelected
              ]}>
                Other
              </Text>
            </TouchableOpacity>
          </View>

          {/* Custom restriction input */}
          {showCustomRestrictionInput && (
            <View style={styles.customInputContainer}>
              <TextInput
                style={styles.customInput}
                placeholder="Enter custom dietary restriction"
                value={customRestriction}
                onChangeText={setCustomRestriction}
                placeholderTextColor={colors.textSecondary}
                onSubmitEditing={addCustomRestriction}
                returnKeyType="done"
              />
              <TouchableOpacity
                style={styles.addButton}
                onPress={addCustomRestriction}
                disabled={!customRestriction.trim()}
              >
                <Text style={[
                  styles.addButtonText,
                  !customRestriction.trim() && styles.addButtonTextDisabled
                ]}>
                  Add
                </Text>
              </TouchableOpacity>
            </View>
          )}

          {/* Display custom restrictions */}
          {preferences.restrictions.filter(restriction => !dietaryOptions.includes(restriction)).length > 0 && (
            <View style={styles.customItemsSection}>
              <Text style={styles.customRestrictionsTitle}>Custom Dietary Restrictions:</Text>
              <View style={styles.customRestrictionsList}>
                {preferences.restrictions
                  .filter(restriction => !dietaryOptions.includes(restriction))
                  .map(restriction => (
                    <View key={restriction} style={styles.customRestrictionItem}>
                      <Text style={styles.customRestrictionText}>{restriction}</Text>
                      <TouchableOpacity
                        style={styles.removeCustomRestrictionButton}
                        onPress={() => removeCustomRestriction(restriction)}
                      >
                        <Ionicons name="close-circle" size={20} color={colors.error} />
                      </TouchableOpacity>
                    </View>
                  ))}
              </View>
            </View>
          )}
        </View>

        {/* Allergies Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Allergies</Text>
          <Text style={styles.sectionSubtitle}>
            Select any food allergies you have
          </Text>
          <View style={styles.optionsContainer}>
            {allergyOptions.map(option =>
              renderOptionButton(
                option,
                preferences.allergies.includes(option),
                () => toggleAllergy(option),
                colors.secondary
              )
            )}
            {/* Other option button */}
            <TouchableOpacity
              style={[
                styles.optionButton,
                (showCustomAllergyInput || preferences.allergies.filter(allergy => !allergyOptions.includes(allergy)).length > 0) && { backgroundColor: colors.secondary, borderColor: colors.secondary }
              ]}
              onPress={() => setShowCustomAllergyInput(!showCustomAllergyInput)}
            >
              <Text style={[
                styles.optionText,
                (showCustomAllergyInput || preferences.allergies.filter(allergy => !allergyOptions.includes(allergy)).length > 0) && styles.optionTextSelected
              ]}>
                Other
              </Text>
            </TouchableOpacity>
          </View>

          {/* Custom allergy input */}
          {showCustomAllergyInput && (
            <View style={styles.customInputContainer}>
              <TextInput
                style={styles.customInput}
                placeholder="Enter custom allergy"
                value={customAllergy}
                onChangeText={setCustomAllergy}
                placeholderTextColor={colors.textSecondary}
                onSubmitEditing={addCustomAllergy}
                returnKeyType="done"
              />
              <TouchableOpacity
                style={styles.addButton}
                onPress={addCustomAllergy}
                disabled={!customAllergy.trim()}
              >
                <Text style={[
                  styles.addButtonText,
                  !customAllergy.trim() && styles.addButtonTextDisabled
                ]}>
                  Add
                </Text>
              </TouchableOpacity>
            </View>
          )}

          {/* Display custom allergies */}
          {preferences.allergies.filter(allergy => !allergyOptions.includes(allergy)).length > 0 && (
            <View style={styles.customAllergiesContainer}>
              <Text style={styles.customAllergiesTitle}>Custom Allergies:</Text>
              <View style={styles.customAllergiesList}>
                {preferences.allergies
                  .filter(allergy => !allergyOptions.includes(allergy))
                  .map(allergy => (
                    <View key={allergy} style={styles.customAllergyItem}>
                      <Text style={styles.customAllergyText}>{allergy}</Text>
                      <TouchableOpacity
                        style={styles.removeCustomAllergyButton}
                        onPress={() => removeCustomAllergy(allergy)}
                      >
                        <Ionicons name="close-circle" size={20} color={colors.error} />
                      </TouchableOpacity>
                    </View>
                  ))}
              </View>
            </View>
          )}
        </View>

        {/* AI Conflict Detection Warning */}
        {conflicts && conflicts.hasConflicts && (
          <View style={styles.conflictSection}>
            <View style={styles.conflictHeader}>
              <Ionicons name="warning" size={20} color={colors.warning} />
              <Text style={styles.conflictTitle}>Dietary Conflicts Detected</Text>
            </View>
            {conflicts.conflicts.map((conflict, index) => (
              <View key={index} style={styles.conflictItem}>
                <Text style={styles.conflictText}>
                  <Text style={styles.conflictItems}>
                    {conflict.items.join(' + ')}
                  </Text>
                  {conflict.type === 'family' && (
                    <Text style={styles.conflictType}> (Family Conflict)</Text>
                  )}
                  {conflict.type === 'user-family' && (
                    <Text style={styles.conflictType}> (User vs Family)</Text>
                  )}
                  : {conflict.reason}
                </Text>
                {conflict.severity === 'high' && (
                  <View style={styles.severityBadge}>
                    <Text style={styles.severityText}>High</Text>
                  </View>
                )}
              </View>
            ))}
            {conflicts.suggestions && conflicts.suggestions.length > 0 && (
              <View style={styles.suggestionsContainer}>
                <Text style={styles.suggestionsTitle}>AI Suggestions:</Text>
                {conflicts.suggestions.map((suggestion, index) => (
                  <Text key={index} style={styles.suggestionText}>
                    • {suggestion}
                  </Text>
                ))}
              </View>
            )}
            <Text style={styles.conflictNote}>
              Note: You can still save these preferences, but consider the conflicts above.
            </Text>
          </View>
        )}

        {/* Loading indicator for conflict checking */}
        {checkingConflicts && (
          <View style={styles.checkingSection}>
            <ActivityIndicator size="small" color={colors.primary} />
            <Text style={styles.checkingText}>Checking for dietary conflicts...</Text>
          </View>
        )}

        {/* Meal Plan Update Information */}
        {showMealPlanUpdate && mealPlanUpdateResult && (
          <View style={styles.updateInfoSection}>
            <View style={styles.updateInfoHeader}>
              <Ionicons name="checkmark-circle" size={20} color={colors.success} />
              <Text style={styles.updateInfoTitle}>Meal Plans Updated!</Text>
            </View>
            <Text style={styles.updateInfoText}>
              {mealPlanUpdateResult.plansUpdated > 0
                ? `${mealPlanUpdateResult.plansUpdated} meal plans updated with ${mealPlanUpdateResult.mealsReplaced} AI-suggested meal replacements.`
                : 'No changes needed to your existing meal plans.'
              }
            </Text>
            {mealPlanUpdateResult.updateDetails && mealPlanUpdateResult.updateDetails.length > 0 && (
              <TouchableOpacity
                style={styles.viewDetailsButton}
                onPress={() => {
                  setShowMealPlanUpdate(false);
                  navigation.navigate('MealPlans');
                }}
              >
                <Text style={styles.viewDetailsText}>View Updated Meal Plans</Text>
                <Ionicons name="arrow-forward" size={16} color={colors.primary} />
              </TouchableOpacity>
            )}
          </View>
        )}



        {/* Calorie Target Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Daily Calorie Target</Text>
          <Text style={styles.sectionSubtitle}>
            Your target daily calorie intake (optional)
          </Text>
          <TextInput
            style={styles.textInput}
            placeholder="e.g., 2000"
            value={preferences.calorieTarget}
            onChangeText={(text) => setPreferences(prev => ({ ...prev, calorieTarget: text }))}
            keyboardType="numeric"
            placeholderTextColor={colors.textSecondary}
          />
        </View>

        {/* Meal Frequency Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Meal Frequency</Text>
          <Text style={styles.sectionSubtitle}>
            How many meals do you prefer per day?
          </Text>
          <View style={styles.frequencyContainer}>
            {[2, 3, 4, 5, 6].map(frequency => (
              <TouchableOpacity
                key={frequency}
                style={[
                  styles.frequencyButton,
                  preferences.mealFrequency === frequency && styles.frequencyButtonSelected
                ]}
                onPress={() => setPreferences(prev => ({ ...prev, mealFrequency: frequency }))}
              >
                <Text style={[
                  styles.frequencyText,
                  preferences.mealFrequency === frequency && styles.frequencyTextSelected
                ]}>
                  {frequency}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Summary Section */}
        <View style={styles.summarySection}>
          <Text style={styles.summaryTitle}>Summary</Text>
          <View style={styles.summaryCard}>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Restrictions:</Text>
              <Text style={styles.summaryValue}>
                {preferences.restrictions.length > 0 ? preferences.restrictions.join(', ') : 'None'}
              </Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Allergies:</Text>
              <Text style={styles.summaryValue}>
                {preferences.allergies.length > 0 ? preferences.allergies.join(', ') : 'None'}
              </Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Daily Calories:</Text>
              <Text style={styles.summaryValue}>
                {preferences.calorieTarget || 'Not set'}
              </Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Meals per day:</Text>
              <Text style={styles.summaryValue}>
                {preferences.mealFrequency}
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Loading Modal */}
      <Modal
        visible={isSaving}
        transparent={true}
        animationType="fade"
        statusBarTranslucent={true}
        onShow={() => console.log('🔄 Loading modal is now visible')}
      >
        <View style={styles.loadingOverlay}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={styles.loadingText}>{savingStatus}</Text>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    backgroundColor: colors.primary,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTitle: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.surface,
  },
  saveButton: {
    fontSize: fonts.sizes.medium,
    color: colors.surface,
    fontWeight: '600',
  },
  section: {
    backgroundColor: colors.surface,
    marginTop: spacing.sm,
    padding: spacing.md,
  },
  sectionTitle: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing.xs,
  },
  sectionSubtitle: {
    fontSize: fonts.sizes.medium,
    color: colors.textSecondary,
    marginBottom: spacing.md,
  },
  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.medium,
    borderWidth: 1,
    borderColor: colors.border,
    marginBottom: spacing.sm,
  },
  optionText: {
    fontSize: fonts.sizes.medium,
    color: colors.text,
    fontWeight: '500',
  },
  optionTextSelected: {
    color: colors.surface,
  },
  checkmark: {
    marginLeft: spacing.xs,
  },
  textInput: {
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: borderRadius.medium,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    fontSize: fonts.sizes.medium,
    color: colors.text,
  },
  frequencyContainer: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  frequencyButton: {
    flex: 1,
    backgroundColor: colors.background,
    paddingVertical: spacing.md,
    borderRadius: borderRadius.medium,
    borderWidth: 1,
    borderColor: colors.border,
    alignItems: 'center',
  },
  frequencyButtonSelected: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  frequencyText: {
    fontSize: fonts.sizes.medium,
    color: colors.text,
    fontWeight: '600',
  },
  frequencyTextSelected: {
    color: colors.surface,
  },
  summarySection: {
    backgroundColor: colors.surface,
    marginTop: spacing.sm,
    padding: spacing.md,
    marginBottom: spacing.xl,
  },
  summaryTitle: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing.md,
  },
  summaryCard: {
    backgroundColor: colors.background,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
    borderWidth: 1,
    borderColor: colors.border,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.sm,
  },
  summaryLabel: {
    fontSize: fonts.sizes.medium,
    color: colors.textSecondary,
    fontWeight: '500',
    flex: 1,
  },
  summaryValue: {
    fontSize: fonts.sizes.medium,
    color: colors.text,
    fontWeight: '600',
    flex: 2,
    textAlign: 'right',
  },
  // Conflict detection styles
  conflictSection: {
    backgroundColor: '#FFF3CD',
    borderColor: colors.warning,
    borderWidth: 1,
    borderRadius: borderRadius.medium,
    margin: spacing.md,
    padding: spacing.md,
  },
  conflictHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  conflictTitle: {
    fontSize: fonts.sizes.medium,
    fontWeight: 'bold',
    color: colors.warning,
    marginLeft: spacing.xs,
  },
  conflictItem: {
    marginBottom: spacing.sm,
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
  },
  conflictText: {
    fontSize: fonts.sizes.small,
    color: colors.text,
    flex: 1,
  },
  conflictItems: {
    fontWeight: 'bold',
    color: colors.warning,
  },
  conflictType: {
    fontWeight: 'bold',
    color: colors.secondary,
    fontSize: fonts.sizes.small,
  },
  severityBadge: {
    backgroundColor: colors.error,
    paddingHorizontal: spacing.xs,
    paddingVertical: 2,
    borderRadius: borderRadius.small,
    marginLeft: spacing.xs,
  },
  severityText: {
    fontSize: fonts.sizes.small,
    color: colors.surface,
    fontWeight: 'bold',
  },
  suggestionsContainer: {
    marginTop: spacing.sm,
    paddingTop: spacing.sm,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  suggestionsTitle: {
    fontSize: fonts.sizes.small,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing.xs,
  },
  suggestionText: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  conflictNote: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    fontStyle: 'italic',
    marginTop: spacing.sm,
  },
  checkingSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.surface,
    margin: spacing.md,
    padding: spacing.md,
    borderRadius: borderRadius.medium,
  },
  checkingText: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginLeft: spacing.sm,
  },
  customInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: spacing.md,
    gap: spacing.sm,
  },
  customInput: {
    flex: 1,
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: borderRadius.medium,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    fontSize: fonts.sizes.medium,
    color: colors.text,
  },
  addButton: {
    backgroundColor: colors.secondary,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.medium,
  },
  addButtonText: {
    color: colors.surface,
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
  },
  addButtonTextDisabled: {
    opacity: 0.5,
  },
  customAllergiesContainer: {
    marginTop: spacing.md,
    padding: spacing.md,
    backgroundColor: colors.backgroundSecondary,
    borderRadius: borderRadius.medium,
  },
  customAllergiesTitle: {
    fontSize: fonts.sizes.small,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing.sm,
  },
  customAllergiesList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  customAllergyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.small,
    borderWidth: 1,
    borderColor: colors.secondary,
  },
  customAllergyText: {
    fontSize: fonts.sizes.small,
    color: colors.text,
    marginRight: spacing.xs,
  },
  removeCustomAllergyButton: {
    padding: 2,
  },
  // Custom restrictions styles (reusing allergy styles)
  customItemsSection: {
    marginTop: spacing.md,
    padding: spacing.md,
    backgroundColor: colors.backgroundSecondary,
    borderRadius: borderRadius.medium,
  },
  customRestrictionsTitle: {
    fontSize: fonts.sizes.small,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing.sm,
  },
  customRestrictionsList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  customRestrictionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.small,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  customRestrictionText: {
    fontSize: fonts.sizes.small,
    color: colors.text,
    marginRight: spacing.xs,
  },
  removeCustomRestrictionButton: {
    padding: 2,
  },
  // Meal plan update styles
  updateInfoSection: {
    backgroundColor: '#E8F5E8',
    borderColor: colors.success,
    borderWidth: 1,
    borderRadius: borderRadius.medium,
    margin: spacing.md,
    padding: spacing.md,
  },
  updateInfoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  updateInfoTitle: {
    fontSize: fonts.sizes.medium,
    fontWeight: 'bold',
    color: colors.success,
    marginLeft: spacing.xs,
  },
  updateInfoText: {
    fontSize: fonts.sizes.small,
    color: colors.text,
    marginBottom: spacing.sm,
    lineHeight: 20,
  },
  viewDetailsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.surface,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderRadius: borderRadius.medium,
    borderWidth: 1,
    borderColor: colors.primary,
    marginTop: spacing.sm,
  },
  viewDetailsText: {
    fontSize: fonts.sizes.small,
    color: colors.primary,
    fontWeight: '600',
    marginRight: spacing.xs,
  },
  // Loading overlay styles
  loadingOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    backgroundColor: colors.surface,
    padding: spacing.xl,
    borderRadius: borderRadius.large,
    alignItems: 'center',
    minWidth: 200,
  },
  loadingText: {
    fontSize: fonts.sizes.medium,
    color: colors.text,
    marginTop: spacing.md,
    textAlign: 'center',
    fontWeight: '500',
  },
});

export default DietaryPreferencesScreen;
