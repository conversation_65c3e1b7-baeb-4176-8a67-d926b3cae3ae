import React, { useState, useEffect } from "react";
import Layout from "../Layout/Layout";
import axios from "axios";
import userAPI from "../../services/userAPI";
import "../../../src/App.css";
import "../DietaryPreferences/DietaryPreferences.css";

const Family = () => {
  const [user, setUser] = useState(null);

  const [members, setMembers] = useState([]);
  const [showForm, setShowForm] = useState(false);
  const [form, setForm] = useState({
    name: "",
    dateOfBirth: "",
    dietaryPreferences: {
      restrictions: [],
      allergies: [],
      dislikedIngredients: []
    }
  });

  // Custom allergy states
  const [customAllergy, setCustomAllergy] = useState('');
  const [showCustomAllergyInput, setShowCustomAllergyInput] = useState(false);

  // Custom restriction states
  const [customRestriction, setCustomRestriction] = useState('');
  const [showCustomRestrictionInput, setShowCustomRestrictionInput] = useState(false);

  // Dietary options (matching main dietary preferences screen)
  const dietaryOptions = [
    'Vegetarian',
    'Vegan',
    'Gluten-Free',
    'Dairy-Free',
    'Nut-Free',
    'Low-Carb',
    'Keto',
    'Pescatarian',
    'Halal',
    'Paleo',
    'Mediterranean',
    'Plant-Based',
    'Organic',
    'High-Protein',
    'Low-Sodium',
    'Sugar-Free',
    'Kosher',
    'Pollotarian',
    'Flexitarian'
  ];

  const allergyOptions = [
    'Milk',
    'Eggs',
    'Fish',
    'Shellfish',
    'Peanuts',
    'Tree Nuts',
    'Wheat',
    'Soybeans',
    'Sesame',
    'Mustard',
    'Celery',
    'Lupin',
    'Mollusks',
    'Sulfites'
  ];

  // Toggle functions for multi-select
  const toggleRestriction = (restriction) => {
    setForm(prev => ({
      ...prev,
      dietaryPreferences: {
        ...prev.dietaryPreferences,
        restrictions: prev.dietaryPreferences.restrictions.includes(restriction)
          ? prev.dietaryPreferences.restrictions.filter(r => r !== restriction)
          : [...prev.dietaryPreferences.restrictions, restriction]
      }
    }));
  };

  const toggleAllergy = (allergy) => {
    setForm(prev => ({
      ...prev,
      dietaryPreferences: {
        ...prev.dietaryPreferences,
        allergies: prev.dietaryPreferences.allergies.includes(allergy)
          ? prev.dietaryPreferences.allergies.filter(a => a !== allergy)
          : [...prev.dietaryPreferences.allergies, allergy]
      }
    }));
  };

  const addDislikedIngredient = (ingredient) => {
    if (ingredient.trim() && !form.dietaryPreferences.dislikedIngredients.includes(ingredient.trim())) {
      setForm(prev => ({
        ...prev,
        dietaryPreferences: {
          ...prev.dietaryPreferences,
          dislikedIngredients: [...prev.dietaryPreferences.dislikedIngredients, ingredient.trim()]
        }
      }));
    }
  };

  const removeDislikedIngredient = (ingredient) => {
    setForm(prev => ({
      ...prev,
      dietaryPreferences: {
        ...prev.dietaryPreferences,
        dislikedIngredients: prev.dietaryPreferences.dislikedIngredients.filter(i => i !== ingredient)
      }
    }));
  };

  // Fetch logged-in user info
  useEffect(() => {
    const fetchUser = async () => {
      try {
        const token = localStorage.getItem("token");
        if (!token) return;
        const res = await axios.get("http://localhost:5000/api/users/profile", {
          headers: { Authorization: `Bearer ${token}` },
        });
        setUser(res.data);
      } catch (err) {
        setUser(null);
      }
    };
    fetchUser();
  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setForm(prev => ({ ...prev, [name]: value }));
  };

  // Calculate age from date of birth
  const calculateAge = (dateOfBirth) => {
    if (!dateOfBirth) return null;

    const today = new Date();
    const birthDate = new Date(dateOfBirth);

    if (isNaN(birthDate.getTime())) return null;

    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  };

  // Get age-based color for family member avatar
  const getAgeBasedColor = (dateOfBirth) => {
    const age = calculateAge(dateOfBirth);

    if (age === null) {
      return '#6c757d'; // Default color for unknown age
    }

    if (age < 13) {
      return '#FF6B6B'; // Red for children (0-12)
    } else if (age < 20) {
      return '#4ECDC4'; // Teal for teenagers (13-19)
    } else if (age < 35) {
      return '#45B7D1'; // Blue for young adults (20-34)
    } else if (age < 55) {
      return '#96CEB4'; // Green for adults (35-54)
    } else {
      return '#FFEAA7'; // Yellow for seniors (55+)
    }
  };

const handleAddMember = async (e) => {
  e.preventDefault();

  // Validate that at least one dietary preference is provided
  const restrictions = form.dietaryPreferences.restrictions;
  const allergies = form.dietaryPreferences.allergies;

  if (restrictions.length === 0 && allergies.length === 0) {
    alert("Please provide at least one dietary restriction or allergy for the family member. This helps us provide better meal recommendations.");
    return;
  }

  try {
    const memberData = {
      name: form.name,
      dateOfBirth: form.dateOfBirth || null,
      dietaryPreferences: form.dietaryPreferences,
    };
    console.log('Adding family member with data:', JSON.stringify(memberData, null, 2));
    const response = await userAPI.addFamilyMember(memberData);
    console.log('Add family member result:', JSON.stringify(response, null, 2));

    if (response.success) {
      setMembers(response.data);
      setForm({
        name: "",
        dateOfBirth: "",
        dietaryPreferences: {
          restrictions: [],
          allergies: [],
          dislikedIngredients: []
        }
      });
      setShowForm(false);
      alert("Family member added successfully!");
    } else {
      alert(response.error || "Failed to add family member.");
    }
  } catch (err) {
    console.error("Add family member error:", err);
    alert("Failed to add family member.");
  }
};

const handleRemoveMember = async (memberId) => {
  const token = localStorage.getItem("token");
  if (!token) return;
  try {
    const res = await axios.delete(
      `http://localhost:5000/api/users/family-members/${memberId}`,
      { headers: { Authorization: `Bearer ${token}` } }
    );
    setMembers(res.data.familyMembers);
  } catch (err) {
    alert("Failed to remove family member.");
  }
};

// Migration mapping for old allergy names to new standardized names
const allergyMigrationMap = {
  'Nuts': 'Tree Nuts',
  'Gluten': 'Wheat',
  'Soy': 'Soybeans',
  'Dairy': 'Milk'
};

// Function to migrate old allergy names to new ones
const migrateAllergies = (allergies) => {
  return allergies.map(allergy => allergyMigrationMap[allergy] || allergy);
};

// Custom allergy functions
const addCustomAllergy = () => {
  if (customAllergy.trim()) {
    // Split by comma and add multiple allergies
    const newAllergies = customAllergy.split(',').map(a => a.trim()).filter(a => a && !form.dietaryPreferences.allergies.includes(a));
    if (newAllergies.length > 0) {
      setForm(prev => ({
        ...prev,
        dietaryPreferences: {
          ...prev.dietaryPreferences,
          allergies: [...prev.dietaryPreferences.allergies, ...newAllergies]
        }
      }));
      setCustomAllergy('');
      setShowCustomAllergyInput(false);
    }
  }
};

const removeCustomAllergy = (allergy) => {
  setForm(prev => {
    const updatedAllergies = prev.dietaryPreferences.allergies.filter(a => a !== allergy);
    const customAllergies = updatedAllergies.filter(a => !allergyOptions.includes(a));

    // Update the custom allergy text field
    setCustomAllergy(customAllergies.join(', '));

    // Hide the input if no custom allergies remain
    if (customAllergies.length === 0) {
      setShowCustomAllergyInput(false);
    }

    return {
      ...prev,
      dietaryPreferences: {
        ...prev.dietaryPreferences,
        allergies: updatedAllergies
      }
    };
  });
};

// Custom restriction functions
const addCustomRestriction = () => {
  if (customRestriction.trim()) {
    // Split by comma and add multiple restrictions
    const newRestrictions = customRestriction.split(',').map(r => r.trim()).filter(r => r && !form.dietaryPreferences.restrictions.includes(r));
    if (newRestrictions.length > 0) {
      setForm(prev => ({
        ...prev,
        dietaryPreferences: {
          ...prev.dietaryPreferences,
          restrictions: [...prev.dietaryPreferences.restrictions, ...newRestrictions]
        }
      }));
      setCustomRestriction('');
      setShowCustomRestrictionInput(false);
    }
  }
};

const removeCustomRestriction = (restriction) => {
  setForm(prev => {
    const updatedRestrictions = prev.dietaryPreferences.restrictions.filter(r => r !== restriction);
    const customRestrictions = updatedRestrictions.filter(r => !dietaryOptions.includes(r));

    // Update the custom restriction text field
    setCustomRestriction(customRestrictions.join(', '));

    // Hide the input if no custom restrictions remain
    if (customRestrictions.length === 0) {
      setShowCustomRestrictionInput(false);
    }

    return {
      ...prev,
      dietaryPreferences: {
        ...prev.dietaryPreferences,
        restrictions: updatedRestrictions
      }
    };
  });
};

// Check if there are custom allergies and set the "Other" option accordingly
useEffect(() => {
  const customAllergies = form.dietaryPreferences.allergies.filter(allergy => !allergyOptions.includes(allergy));
  if (customAllergies.length > 0) {
    setShowCustomAllergyInput(true);
    setCustomAllergy(customAllergies.join(', '));
  }
}, [form.dietaryPreferences.allergies]);

// Check if there are custom restrictions and set the "Other" option accordingly
useEffect(() => {
  const customRestrictions = form.dietaryPreferences.restrictions.filter(restriction => !dietaryOptions.includes(restriction));
  if (customRestrictions.length > 0) {
    setShowCustomRestrictionInput(true);
    setCustomRestriction(customRestrictions.join(', '));
  }
}, [form.dietaryPreferences.restrictions]);

useEffect(() => {
  // Fetch family members from backend
  const fetchFamilyMembers = async () => {
    try {
      const response = await userAPI.getFamilyMembers();
      if (response.success) {
        // Migrate old allergy names for all family members
        const migratedMembers = (response.data || []).map(member => ({
          ...member,
          dietaryPreferences: {
            ...member.dietaryPreferences,
            allergies: migrateAllergies(member.dietaryPreferences?.allergies || [])
          }
        }));
        setMembers(migratedMembers);
      } else {
        console.error("Failed to fetch family members:", response.error);
        setMembers([]);
      }
    } catch (err) {
      console.error("Failed to fetch family members:", err);
      setMembers([]);
    }
  };
  fetchFamilyMembers();
}, []);

  return (
    <Layout>
      <div className="main-content">
        <div className="family-container-modern">
          <div className="family-header-modern">
            <div className="family-title-section">
              <h1>Family Profile</h1>
              <p className="family-subtitle">Manage your family members and their dietary preferences</p>
            </div>
            {user && (
              <div className="current-user-card">
                <div className="user-avatar">
                  {user.firstName?.charAt(0)}{user.lastName?.charAt(0)}
                </div>
                <div className="user-info">
                  <span className="user-name">{user.firstName} {user.lastName}</span>
                  <span className="user-role">Family Admin</span>
                </div>
              </div>
            )}
          </div>

          <div className="family-members-section">
            <div className="section-header">
              <div>
                <h2>Family Members</h2>
                <p className="section-subtitle">Manage dietary preferences for your family</p>
              </div>
              <button
                className="add-member-btn-modern"
                onClick={() => setShowForm((prev) => !prev)}
              >
                {showForm ? (
                  <>
                    <span>✕</span>
                    Cancel
                  </>
                ) : (
                  <>
                    <span>+</span>
                    Add Member
                  </>
                )}
              </button>
            </div>

            {showForm && (
              <div className="add-member-card">
                <div className="card-header">
                  <h3>Add New Family Member</h3>
                  <p className="card-subtitle">Enter their dietary information</p>
                </div>
                <form className="modern-form" onSubmit={handleAddMember}>
                  <div className="form-grid">
                    <div className="form-group">
                      <label className="form-label">
                        <span className="label-text">Name *</span>
                        <input
                          type="text"
                          name="name"
                          value={form.name}
                          onChange={handleInputChange}
                          required
                          className="form-input"
                          placeholder="Enter family member's name"
                        />
                      </label>
                    </div>
                    <div className="form-group">
                      <label className="form-label">
                        <span className="label-text">Date of Birth (Optional)</span>
                        <input
                          type="date"
                          name="dateOfBirth"
                          value={form.dateOfBirth}
                          onChange={handleInputChange}
                          className="form-input"
                          max={new Date().toISOString().split('T')[0]}
                        />
                        <small className="form-hint">Used to calculate age and provide age-appropriate meal suggestions</small>
                      </label>
                    </div>
                    {/* Dietary Restrictions */}
                    <div className="preference-section">
                      <h3>Dietary Restrictions <span className="required">* Required</span></h3>
                      <p>Select any dietary restrictions for this family member:</p>
                      <div className="options-grid">
                        {dietaryOptions.map((option) => (
                          <label key={option} className="option-checkbox">
                            <input
                              type="checkbox"
                              checked={form.dietaryPreferences.restrictions.includes(option)}
                              onChange={() => toggleRestriction(option)}
                            />
                            <span className="checkmark"></span>
                            {option}
                          </label>
                        ))}
                        {/* Other option */}
                        <label className="option-checkbox">
                          <input
                            type="checkbox"
                            checked={showCustomRestrictionInput || form.dietaryPreferences.restrictions.filter(restriction => !dietaryOptions.includes(restriction)).length > 0}
                            onChange={() => setShowCustomRestrictionInput(!showCustomRestrictionInput)}
                          />
                          <span className="checkmark"></span>
                          Other
                        </label>
                      </div>

                      {/* Custom restriction input */}
                      {showCustomRestrictionInput && (
                        <div className="custom-input-container">
                          <input
                            type="text"
                            className="custom-input"
                            placeholder="Enter custom dietary restriction (separate multiple with commas)"
                            value={customRestriction}
                            onChange={(e) => setCustomRestriction(e.target.value)}
                            onKeyPress={(e) => e.key === 'Enter' && addCustomRestriction()}
                          />
                          <button
                            type="button"
                            className="add-button"
                            onClick={addCustomRestriction}
                            disabled={!customRestriction.trim()}
                          >
                            Add
                          </button>
                        </div>
                      )}

                      {/* Display custom restrictions */}
                      {form.dietaryPreferences.restrictions.filter(restriction => !dietaryOptions.includes(restriction)).length > 0 && (
                        <div className="custom-items-display">
                          <h4>Custom Dietary Restrictions:</h4>
                          <div className="custom-items-list">
                            {form.dietaryPreferences.restrictions
                              .filter(restriction => !dietaryOptions.includes(restriction))
                              .map(restriction => (
                                <div key={restriction} className="custom-restriction-item">
                                  <span>{restriction}</span>
                                  <button
                                    type="button"
                                    className="remove-custom-restriction"
                                    onClick={() => removeCustomRestriction(restriction)}
                                    title="Remove restriction"
                                  >
                                    ×
                                  </button>
                                </div>
                              ))}
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Allergies */}
                    <div className="preference-section">
                      <h3>Allergies</h3>
                      <p>Select any food allergies for this family member:</p>
                      <div className="options-grid">
                        {allergyOptions.map((option) => (
                          <label key={option} className="option-checkbox">
                            <input
                              type="checkbox"
                              checked={form.dietaryPreferences.allergies.includes(option)}
                              onChange={() => toggleAllergy(option)}
                            />
                            <span className="checkmark"></span>
                            {option}
                          </label>
                        ))}
                        {/* Other option */}
                        <label className="option-checkbox">
                          <input
                            type="checkbox"
                            checked={showCustomAllergyInput || form.dietaryPreferences.allergies.filter(allergy => !allergyOptions.includes(allergy)).length > 0}
                            onChange={() => setShowCustomAllergyInput(!showCustomAllergyInput)}
                          />
                          <span className="checkmark"></span>
                          Other
                        </label>
                      </div>

                      {/* Custom allergy input */}
                      {showCustomAllergyInput && (
                        <div className="custom-input-container">
                          <input
                            type="text"
                            className="custom-input"
                            placeholder="Enter custom allergy (separate multiple with commas)"
                            value={customAllergy}
                            onChange={(e) => setCustomAllergy(e.target.value)}
                            onKeyPress={(e) => e.key === 'Enter' && addCustomAllergy()}
                          />
                          <button
                            type="button"
                            className="add-button"
                            onClick={addCustomAllergy}
                            disabled={!customAllergy.trim()}
                          >
                            Add
                          </button>
                        </div>
                      )}

                      {/* Display custom allergies */}
                      {form.dietaryPreferences.allergies.filter(allergy => !allergyOptions.includes(allergy)).length > 0 && (
                        <div className="custom-allergies-container">
                          <h4>Custom Allergies:</h4>
                          <div className="custom-allergies-list">
                            {form.dietaryPreferences.allergies
                              .filter(allergy => !allergyOptions.includes(allergy))
                              .map(allergy => (
                                <div key={allergy} className="custom-allergy-item">
                                  <span>{allergy}</span>
                                  <button
                                    type="button"
                                    className="remove-custom-allergy"
                                    onClick={() => removeCustomAllergy(allergy)}
                                    title="Remove allergy"
                                  >
                                    ×
                                  </button>
                                </div>
                              ))}
                          </div>
                        </div>
                      )}
                    </div>
                    {/* Disliked Ingredients */}
                    <div className="preference-section">
                      <h3>Disliked Ingredients</h3>
                      <p>Add any ingredients this family member dislikes:</p>
                      <div className="disliked-ingredients-input">
                        <input
                          type="text"
                          placeholder="Enter ingredient and press Enter"
                          className="form-input"
                          onKeyPress={(e) => {
                            if (e.key === 'Enter') {
                              e.preventDefault();
                              addDislikedIngredient(e.target.value);
                              e.target.value = '';
                            }
                          }}
                        />
                      </div>
                      {form.dietaryPreferences.dislikedIngredients.length > 0 && (
                        <div className="disliked-ingredients-list">
                          {form.dietaryPreferences.dislikedIngredients.map((ingredient, index) => (
                            <div key={index} className="disliked-ingredient-item">
                              <span>{ingredient}</span>
                              <button
                                type="button"
                                className="remove-ingredient"
                                onClick={() => removeDislikedIngredient(ingredient)}
                              >
                                ×
                              </button>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="form-actions">
                    <button type="submit" className="btn-primary-modern">
                      Add Family Member
                    </button>
                  </div>
                </form>
              </div>
            )}

            {members.length === 0 ? (
              <div className="empty-state">
                <div className="empty-state-icon">👥</div>
                <h3>No family members yet</h3>
                <p>Add family members to manage their dietary preferences and create personalized meal plans.</p>
                <button
                  className="btn-primary-modern"
                  onClick={() => setShowForm(true)}
                >
                  Add Your First Member
                </button>
              </div>
            ) : (
              <>
                {/* Age Color Legend */}
                <div className="age-legend">
                  <h4>Age Color Guide:</h4>
                  <div className="age-legend-items">
                    <div className="age-legend-item">
                      <div className="age-legend-color" style={{ backgroundColor: '#FF6B6B' }}></div>
                      <span>Children (0-12)</span>
                    </div>
                    <div className="age-legend-item">
                      <div className="age-legend-color" style={{ backgroundColor: '#4ECDC4' }}></div>
                      <span>Teens (13-19)</span>
                    </div>
                    <div className="age-legend-item">
                      <div className="age-legend-color" style={{ backgroundColor: '#45B7D1' }}></div>
                      <span>Young Adults (20-34)</span>
                    </div>
                    <div className="age-legend-item">
                      <div className="age-legend-color" style={{ backgroundColor: '#96CEB4' }}></div>
                      <span>Adults (35-54)</span>
                    </div>
                    <div className="age-legend-item">
                      <div className="age-legend-color" style={{ backgroundColor: '#FFEAA7' }}></div>
                      <span>Seniors (55+)</span>
                    </div>
                  </div>
                </div>

                <div className="family-members-grid">
                {members.map((member, idx) => (
                  <div key={member._id || idx} className="member-card">
                    <div className="member-header">
                      <div className="member-avatar" style={{
                        backgroundColor: getAgeBasedColor(member.dateOfBirth)
                      }}>
                        {member.name?.charAt(0)?.toUpperCase()}
                      </div>
                      <div className="member-info">
                        <h3 className="member-name">{member.name}</h3>
                        <span className="member-role">Family Member</span>
                        {member.dateOfBirth && calculateAge(member.dateOfBirth) && (
                          <span className="member-age">Age: {calculateAge(member.dateOfBirth)} years</span>
                        )}
                      </div>
                      <button
                        className="remove-btn"
                        onClick={() => handleRemoveMember(member._id)}
                        title="Remove member"
                      >
                        ✕
                      </button>
                    </div>
                    <div className="member-details">
                      {member.dateOfBirth && (
                        <div className="detail-item">
                          <span className="detail-label">Date of Birth</span>
                          <span className="detail-value">
                            {new Date(member.dateOfBirth).toLocaleDateString()}
                            {calculateAge(member.dateOfBirth) && (
                              <span className="age-display"> (Age: {calculateAge(member.dateOfBirth)} years)</span>
                            )}
                          </span>
                        </div>
                      )}
                      <div className="detail-item">
                        <span className="detail-label">Dietary Preferences</span>
                        <span className="detail-value">
                          {member.dietaryPreferences?.restrictions?.length
                            ? member.dietaryPreferences.restrictions.join(", ")
                            : "None specified"}
                        </span>
                      </div>
                      <div className="detail-item">
                        <span className="detail-label">Allergies</span>
                        <span className="detail-value">
                          {member.dietaryPreferences?.allergies?.length
                            ? member.dietaryPreferences.allergies.join(", ")
                            : "None specified"}
                        </span>
                      </div>
                      <div className="detail-item">
                        <span className="detail-label">Disliked Ingredients</span>
                        <span className="detail-value">
                          {member.dietaryPreferences?.dislikedIngredients?.length
                            ? member.dietaryPreferences.dislikedIngredients.join(", ")
                            : "None specified"}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              </>
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Family;
