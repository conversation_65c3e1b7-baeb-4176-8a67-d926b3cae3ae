import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  FlatList,
  Modal,
  TextInput,
  Alert,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { userAPI, aiAPI } from '../../services/api';
import { useAuth } from '../../context/AuthContext';
import { colors, legacyFonts as fonts, legacySpacing as spacing, borderRadius } from '../../styles/theme';
import { commonStyles } from '../../styles/commonStyles';

const FamilyScreen = ({ navigation }) => {
  const [familyMembers, setFamilyMembers] = useState([]);
  const [userDietaryPreferences, setUserDietaryPreferences] = useState({});
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [showAddMemberModal, setShowAddMemberModal] = useState(false);
  const [showEditPreferencesModal, setShowEditPreferencesModal] = useState(false);
  const [editingMember, setEditingMember] = useState(null);
  const [conflicts, setConflicts] = useState(null);
  const [checkingConflicts, setCheckingConflicts] = useState(false);
  const [showAgeLegend, setShowAgeLegend] = useState(false);

  // Custom allergy states for new family member
  const [customAllergyNewMember, setCustomAllergyNewMember] = useState('');
  const [showCustomAllergyInputNewMember, setShowCustomAllergyInputNewMember] = useState(false);

  // Custom restriction states for new family member
  const [customRestrictionNewMember, setCustomRestrictionNewMember] = useState('');
  const [showCustomRestrictionInputNewMember, setShowCustomRestrictionInputNewMember] = useState(false);

  // Custom allergy states for user preferences
  const [customAllergyUser, setCustomAllergyUser] = useState('');
  const [showCustomAllergyInputUser, setShowCustomAllergyInputUser] = useState(false);

  // Custom restriction states for user preferences
  const [customRestrictionUser, setCustomRestrictionUser] = useState('');
  const [showCustomRestrictionInputUser, setShowCustomRestrictionInputUser] = useState(false);
  const [newMember, setNewMember] = useState({
    name: '',
    dateOfBirth: '',
    dietaryPreferences: {
      restrictions: [],
      allergies: [],
      dislikedIngredients: [],
      calorieTarget: '',
      mealFrequency: 3
    }
  });

  const { user } = useAuth();

  // Check if there are custom restrictions for new member and set the "Other" option accordingly
  useEffect(() => {
    const customRestrictions = newMember.dietaryPreferences.restrictions.filter(restriction => !dietaryOptions.includes(restriction));
    if (customRestrictions.length > 0) {
      setShowCustomRestrictionInputNewMember(true);
      setCustomRestrictionNewMember(customRestrictions.join(', '));
    }
  }, [newMember.dietaryPreferences.restrictions]);

  // Check if there are custom allergies for new member and set the "Other" option accordingly
  useEffect(() => {
    const customAllergies = newMember.dietaryPreferences.allergies.filter(allergy => !allergyOptions.includes(allergy));
    if (customAllergies.length > 0) {
      setShowCustomAllergyInputNewMember(true);
      setCustomAllergyNewMember(customAllergies.join(', '));
    }
  }, [newMember.dietaryPreferences.allergies]);

  // Check if there are custom restrictions for user and set the "Other" option accordingly
  useEffect(() => {
    const customRestrictions = (userDietaryPreferences.restrictions || []).filter(restriction => !dietaryOptions.includes(restriction));
    if (customRestrictions.length > 0) {
      setShowCustomRestrictionInputUser(true);
      setCustomRestrictionUser(customRestrictions.join(', '));
    }
  }, [userDietaryPreferences.restrictions]);

  // Check if there are custom allergies for user and set the "Other" option accordingly
  useEffect(() => {
    const customAllergies = (userDietaryPreferences.allergies || []).filter(allergy => !allergyOptions.includes(allergy));
    if (customAllergies.length > 0) {
      setShowCustomAllergyInputUser(true);
      setCustomAllergyUser(customAllergies.join(', '));
    }
  }, [userDietaryPreferences.allergies]);

  // Helper functions for multi-select
  const toggleNewMemberRestriction = (restriction) => {
    setNewMember(prev => ({
      ...prev,
      dietaryPreferences: {
        ...prev.dietaryPreferences,
        restrictions: prev.dietaryPreferences.restrictions.includes(restriction)
          ? prev.dietaryPreferences.restrictions.filter(r => r !== restriction)
          : [...prev.dietaryPreferences.restrictions, restriction]
      }
    }));
  };

  const toggleNewMemberAllergy = (allergy) => {
    setNewMember(prev => ({
      ...prev,
      dietaryPreferences: {
        ...prev.dietaryPreferences,
        allergies: prev.dietaryPreferences.allergies.includes(allergy)
          ? prev.dietaryPreferences.allergies.filter(a => a !== allergy)
          : [...prev.dietaryPreferences.allergies, allergy]
      }
    }));
  };

  // Custom allergy functions for new family member
  const addCustomAllergyNewMember = () => {
    if (customAllergyNewMember.trim()) {
      // Split by comma and add multiple allergies
      const newAllergies = customAllergyNewMember.split(',').map(a => a.trim()).filter(a => a && !newMember.dietaryPreferences.allergies.includes(a));
      if (newAllergies.length > 0) {
        setNewMember(prev => ({
          ...prev,
          dietaryPreferences: {
            ...prev.dietaryPreferences,
            allergies: [...prev.dietaryPreferences.allergies, ...newAllergies]
          }
        }));
        setCustomAllergyNewMember('');
        setShowCustomAllergyInputNewMember(false);
      }
    }
  };

  const removeCustomAllergyNewMember = (allergy) => {
    setNewMember(prev => {
      const updatedAllergies = prev.dietaryPreferences.allergies.filter(a => a !== allergy);
      const customAllergies = updatedAllergies.filter(a => !allergyOptions.includes(a));

      // Update the custom allergy text field
      setCustomAllergyNewMember(customAllergies.join(', '));

      // Hide the input if no custom allergies remain
      if (customAllergies.length === 0) {
        setShowCustomAllergyInputNewMember(false);
      }

      return {
        ...prev,
        dietaryPreferences: {
          ...prev.dietaryPreferences,
          allergies: updatedAllergies
        }
      };
    });
  };

  // Custom restriction functions for new family member
  const addCustomRestrictionNewMember = () => {
    if (customRestrictionNewMember.trim()) {
      // Split by comma and add multiple restrictions
      const newRestrictions = customRestrictionNewMember.split(',').map(r => r.trim()).filter(r => r && !newMember.dietaryPreferences.restrictions.includes(r));
      if (newRestrictions.length > 0) {
        setNewMember(prev => ({
          ...prev,
          dietaryPreferences: {
            ...prev.dietaryPreferences,
            restrictions: [...prev.dietaryPreferences.restrictions, ...newRestrictions]
          }
        }));
        setCustomRestrictionNewMember('');
        setShowCustomRestrictionInputNewMember(false);
      }
    }
  };

  const removeCustomRestrictionNewMember = (restriction) => {
    setNewMember(prev => {
      const updatedRestrictions = prev.dietaryPreferences.restrictions.filter(r => r !== restriction);
      const customRestrictions = updatedRestrictions.filter(r => !dietaryOptions.includes(r));

      // Update the custom restriction text field
      setCustomRestrictionNewMember(customRestrictions.join(', '));

      // Hide the input if no custom restrictions remain
      if (customRestrictions.length === 0) {
        setShowCustomRestrictionInputNewMember(false);
      }

      return {
        ...prev,
        dietaryPreferences: {
          ...prev.dietaryPreferences,
          restrictions: updatedRestrictions
        }
      };
    });
  };

  const toggleUserRestriction = (restriction) => {
    setUserDietaryPreferences(prev => ({
      ...prev,
      restrictions: (prev.restrictions || []).includes(restriction)
        ? (prev.restrictions || []).filter(r => r !== restriction)
        : [...(prev.restrictions || []), restriction]
    }));
  };

  const toggleUserAllergy = (allergy) => {
    setUserDietaryPreferences(prev => ({
      ...prev,
      allergies: (prev.allergies || []).includes(allergy)
        ? (prev.allergies || []).filter(a => a !== allergy)
        : [...(prev.allergies || []), allergy]
    }));
  };

  // Custom restriction functions for user preferences
  const addCustomRestrictionUser = () => {
    if (customRestrictionUser.trim()) {
      const newRestrictions = customRestrictionUser.split(',').map(r => r.trim()).filter(r => r && !(userDietaryPreferences.restrictions || []).includes(r));
      if (newRestrictions.length > 0) {
        setUserDietaryPreferences(prev => ({
          ...prev,
          restrictions: [...(prev.restrictions || []), ...newRestrictions]
        }));
        setCustomRestrictionUser('');
        setShowCustomRestrictionInputUser(false);
      }
    }
  };

  const removeCustomRestrictionUser = (restriction) => {
    setUserDietaryPreferences(prev => {
      const updatedRestrictions = (prev.restrictions || []).filter(r => r !== restriction);
      const customRestrictions = updatedRestrictions.filter(r => !dietaryOptions.includes(r));

      setCustomRestrictionUser(customRestrictions.join(', '));

      if (customRestrictions.length === 0) {
        setShowCustomRestrictionInputUser(false);
      }

      return {
        ...prev,
        restrictions: updatedRestrictions
      };
    });
  };

  // Custom allergy functions for user preferences
  const addCustomAllergyUser = () => {
    if (customAllergyUser.trim()) {
      const newAllergies = customAllergyUser.split(',').map(a => a.trim()).filter(a => a && !(userDietaryPreferences.allergies || []).includes(a));
      if (newAllergies.length > 0) {
        setUserDietaryPreferences(prev => ({
          ...prev,
          allergies: [...(prev.allergies || []), ...newAllergies]
        }));
        setCustomAllergyUser('');
        setShowCustomAllergyInputUser(false);
      }
    }
  };

  const removeCustomAllergyUser = (allergy) => {
    setUserDietaryPreferences(prev => {
      const updatedAllergies = (prev.allergies || []).filter(a => a !== allergy);
      const customAllergies = updatedAllergies.filter(a => !allergyOptions.includes(a));

      setCustomAllergyUser(customAllergies.join(', '));

      if (customAllergies.length === 0) {
        setShowCustomAllergyInputUser(false);
      }

      return {
        ...prev,
        allergies: updatedAllergies
      };
    });
  };

  // Custom allergy functions for user preferences
  const addCustomAllergyUser = () => {
    if (customAllergyUser.trim()) {
      // Split by comma and add multiple allergies
      const newAllergies = customAllergyUser.split(',').map(a => a.trim()).filter(a => a && !(userDietaryPreferences.allergies || []).includes(a));
      if (newAllergies.length > 0) {
        setUserDietaryPreferences(prev => ({
          ...prev,
          allergies: [...(prev.allergies || []), ...newAllergies]
        }));
        setCustomAllergyUser('');
        setShowCustomAllergyInputUser(false);
      }
    }
  };

  const removeCustomAllergyUser = (allergy) => {
    setUserDietaryPreferences(prev => {
      const updatedAllergies = (prev.allergies || []).filter(a => a !== allergy);
      const customAllergies = updatedAllergies.filter(a => !allergyOptions.includes(a));

      // Update the custom allergy text field
      setCustomAllergyUser(customAllergies.join(', '));

      // Hide the input if no custom allergies remain
      if (customAllergies.length === 0) {
        setShowCustomAllergyInputUser(false);
      }

      return {
        ...prev,
        allergies: updatedAllergies
      };
    });
  };

  // Conflict detection function
  const checkDietaryConflicts = async (currentPreferences) => {
    try {
      setCheckingConflicts(true);

      // Prepare family member preferences for conflict checking
      const familyPreferences = familyMembers.map(member => ({
        name: member.name,
        restrictions: member.dietaryPreferences?.restrictions || [],
        allergies: member.dietaryPreferences?.allergies || [],
        dislikedIngredients: member.dietaryPreferences?.dislikedIngredients || []
      }));

      const requestData = {
        userPreferences: {
          restrictions: currentPreferences.restrictions || [],
          allergies: currentPreferences.allergies || [],
          dislikedIngredients: currentPreferences.dislikedIngredients || []
        },
        familyMembers: familyPreferences
      };

      const response = await aiAPI.detectDietaryConflicts(requestData);

      if (response.data.success && response.data.conflicts) {
        setConflicts(response.data.conflicts);
      } else {
        setConflicts(null);
      }
    } catch (error) {
      console.error('Error checking dietary conflicts:', error);
      setConflicts(null);
    } finally {
      setCheckingConflicts(false);
    }
  };

  const renderOptionButton = (option, isSelected, onPress, color = colors.primary) => (
    <TouchableOpacity
      key={option}
      style={[
        styles.optionButton,
        isSelected && { backgroundColor: color, borderColor: color }
      ]}
      onPress={onPress}
    >
      <Text style={[
        styles.optionText,
        isSelected && styles.optionTextSelected
      ]}>
        {option}
      </Text>
      {isSelected && (
        <Ionicons name="checkmark" size={16} color={colors.surface} style={styles.checkmark} />
      )}
    </TouchableOpacity>
  );

  // Calculate age from date of birth
  const calculateAge = (dateOfBirth) => {
    if (!dateOfBirth) return null;

    const today = new Date();
    const birthDate = new Date(dateOfBirth);

    if (isNaN(birthDate.getTime())) return null;

    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  };

  // Get age-based color for family member avatar
  const getAgeBasedColor = (dateOfBirth) => {
    const age = calculateAge(dateOfBirth);

    if (age === null) {
      return colors.textSecondary; // Default color for unknown age
    }

    if (age < 13) {
      return '#FF6B6B'; // Red for children (0-12)
    } else if (age < 20) {
      return '#4ECDC4'; // Teal for teenagers (13-19)
    } else if (age < 35) {
      return '#45B7D1'; // Blue for young adults (20-34)
    } else if (age < 55) {
      return '#96CEB4'; // Green for adults (35-54)
    } else {
      return '#FFEAA7'; // Yellow for seniors (55+)
    }
  };

  const dietaryOptions = [
    'Vegetarian',
    'Vegan',
    'Dairy-Free',
    'Egg-Free',
    'Gluten-Free',
    'Soy-Free',
    'Nut-Free',
    'Low-Carb',
    'Low-Sugar',
    'Sugar-Free',
    'Low-Fat',
    'Low-Sodium',
    'Organic',
    'Halal',
    'High-Protein',
    'Pescatarian',
    'Keto',
    'Plant-Based',
    'Kosher',
    'Climatarian',
    'Raw Food',
    'Mediterranean',
    'Paleo',
    'Kangatarian',
    'Pollotarian',
    'Flexitarian'
  ];

  const allergyOptions = [
    'Milk',
    'Eggs',
    'Fish',
    'Shellfish',
    'Peanuts',
    'Tree Nuts',
    'Wheat',
    'Soybeans',
    'Sesame',
    'Mustard',
    'Celery',
    'Lupin',
    'Mollusks',
    'Sulfites'
  ];

  // Migration mapping for old allergy names to new standardized names
  const allergyMigrationMap = {
    'Nuts': 'Tree Nuts',
    'Gluten': 'Wheat',
    'Soy': 'Soybeans',
    'Dairy': 'Milk'
  };

  // Function to migrate old allergy names to new ones
  const migrateAllergies = (allergies) => {
    return allergies.map(allergy => allergyMigrationMap[allergy] || allergy);
  };

  // Check if there are custom allergies and set the "Other" option accordingly for new member
  useEffect(() => {
    const customAllergies = newMember.dietaryPreferences.allergies.filter(allergy => !allergyOptions.includes(allergy));
    if (customAllergies.length > 0) {
      setShowCustomAllergyInputNewMember(true);
      setCustomAllergyNewMember(customAllergies.join(', '));
    }
  }, [newMember.dietaryPreferences.allergies]);

  // Check if there are custom allergies and set the "Other" option accordingly for user
  useEffect(() => {
    const customAllergies = (userDietaryPreferences.allergies || []).filter(allergy => !allergyOptions.includes(allergy));
    if (customAllergies.length > 0) {
      setShowCustomAllergyInputUser(true);
      setCustomAllergyUser(customAllergies.join(', '));
    }
  }, [userDietaryPreferences.allergies]);

  useEffect(() => {
    loadFamilyData();
  }, []);

  // Check for conflicts whenever user preferences change
  useEffect(() => {
    if (showEditPreferencesModal &&
        (userDietaryPreferences.restrictions?.length > 0 || userDietaryPreferences.allergies?.length > 0)) {
      const timeoutId = setTimeout(() => {
        checkDietaryConflicts(userDietaryPreferences);
      }, 1000); // Debounce for 1 second

      return () => clearTimeout(timeoutId);
    } else {
      setConflicts(null);
    }
  }, [userDietaryPreferences.restrictions, userDietaryPreferences.allergies, familyMembers, showEditPreferencesModal]);

  const loadFamilyData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        loadFamilyMembers(),
        loadUserDietaryPreferences(),
      ]);
    } catch (error) {
      console.error('Error loading family data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadFamilyMembers = async () => {
    try {
      const response = await userAPI.getFamilyMembers();
      setFamilyMembers(response.data || []);
    } catch (error) {
      console.error('Error loading family members:', error);
    }
  };

  const loadUserDietaryPreferences = async () => {
    try {
      const response = await userAPI.getDietaryPreferences();
      // Handle nested dietaryPreferences structure from backend
      const preferences = response.data?.dietaryPreferences || response.data || {};

      // Migrate old allergy names to new standardized names
      if (preferences.allergies) {
        preferences.allergies = migrateAllergies(preferences.allergies);
      }

      setUserDietaryPreferences(preferences);
    } catch (error) {
      console.error('Error loading dietary preferences:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadFamilyData();
    setRefreshing(false);
  };

  const handleAddMember = async () => {
    try {
      if (!newMember.name.trim()) {
        Alert.alert('Error', 'Please enter a name for the family member');
        return;
      }

      // Check if at least one dietary preference is selected (restrictions or allergies)
      const hasRestrictions = newMember.dietaryPreferences.restrictions.length > 0;
      const hasAllergies = newMember.dietaryPreferences.allergies.length > 0;

      if (!hasRestrictions && !hasAllergies) {
        Alert.alert(
          'Dietary Preferences Required',
          'Please select at least one dietary restriction or allergy for the family member. This helps us provide better meal recommendations.',
          [{ text: 'OK' }]
        );
        return;
      }

      await userAPI.addFamilyMember(newMember);
      setShowAddMemberModal(false);
      setNewMember({
        name: '',
        dateOfBirth: '',
        dietaryPreferences: {
          restrictions: [],
          allergies: [],
          dislikedIngredients: [],
          calorieTarget: '',
          mealFrequency: 3
        }
      });
      await loadFamilyMembers();
      Alert.alert('Success', 'Family member added successfully');
    } catch (error) {
      console.error('Error adding family member:', error);
      Alert.alert('Error', 'Failed to add family member');
    }
  };

  const handleRemoveMember = (memberId) => {
    Alert.alert(
      'Remove Family Member',
      'Are you sure you want to remove this family member?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: async () => {
            try {
              await userAPI.removeFamilyMember(memberId);
              await loadFamilyMembers();
              Alert.alert('Success', 'Family member removed successfully');
            } catch (error) {
              console.error('Error removing family member:', error);
              Alert.alert('Error', 'Failed to remove family member');
            }
          }
        }
      ]
    );
  };

  const handleUpdateUserPreferences = async () => {
    try {
      // Check if at least one dietary preference is selected (restrictions or allergies)
      const hasRestrictions = (userDietaryPreferences.restrictions || []).length > 0;
      const hasAllergies = (userDietaryPreferences.allergies || []).length > 0;

      if (!hasRestrictions && !hasAllergies) {
        Alert.alert(
          'Dietary Preferences Required',
          'Please select at least one dietary restriction or allergy. This helps us provide better meal recommendations.',
          [{ text: 'OK' }]
        );
        return;
      }

      await userAPI.updateDietaryPreferences(userDietaryPreferences);
      setShowEditPreferencesModal(false);
      setConflicts(null);
      Alert.alert('Success', 'Dietary preferences updated successfully');
    } catch (error) {
      console.error('Error updating dietary preferences:', error);
      Alert.alert('Error', 'Failed to update dietary preferences');
    }
  };

  const renderFamilyMember = ({ item: member }) => (
    <View style={styles.memberCard}>
      <View style={styles.memberHeader}>
        <View style={[
          styles.memberAvatar,
          { backgroundColor: getAgeBasedColor(member.dateOfBirth) }
        ]}>
          <Text style={styles.memberAvatarText}>
            {member.name.charAt(0).toUpperCase()}
          </Text>
        </View>
        <View style={styles.memberInfo}>
          <Text style={styles.memberName}>{member.name}</Text>
          {member.dateOfBirth && (
            <Text style={styles.memberAge}>
              Age: {calculateAge(member.dateOfBirth)} years old
            </Text>
          )}
          <Text style={styles.memberPreferences}>
            {member.dietaryPreferences?.restrictions?.length > 0
              ? member.dietaryPreferences.restrictions.join(', ')
              : 'No dietary restrictions'
            }
          </Text>
        </View>
        <TouchableOpacity
          style={styles.removeButton}
          onPress={() => handleRemoveMember(member._id)}
        >
          <Ionicons name="close-circle" size={24} color={colors.secondary} />
        </TouchableOpacity>
      </View>

      {member.dietaryPreferences?.allergies?.length > 0 && (
        <View style={styles.allergiesSection}>
          <Text style={styles.allergiesLabel}>Allergies:</Text>
          <Text style={styles.allergiesText}>
            {member.dietaryPreferences.allergies.join(', ')}
          </Text>
        </View>
      )}
    </View>
  );

  return (
    <SafeAreaView style={commonStyles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={colors.surface} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Family Profile</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => setShowAddMemberModal(true)}
        >
          <Ionicons name="add" size={24} color={colors.surface} />
        </TouchableOpacity>
      </View>

      <ScrollView
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
          />
        }
      >
        {/* Family Members Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeaderWithLegend}>
            <Text style={styles.sectionTitle}>Family Members</Text>
            <TouchableOpacity
              style={styles.ageLegendButton}
              onPress={() => setShowAgeLegend(!showAgeLegend)}
            >
              <Ionicons name="color-palette-outline" size={20} color={colors.primary} />
              <Text style={styles.ageLegendButtonText}>Age Colors</Text>
            </TouchableOpacity>
          </View>

          {/* Age Legend */}
          {showAgeLegend && (
            <View style={styles.ageLegend}>
              <Text style={styles.ageLegendTitle}>Age Color Guide:</Text>
              <View style={styles.ageLegendItems}>
                <View style={styles.ageLegendItem}>
                  <View style={[styles.ageLegendColor, { backgroundColor: '#FF6B6B' }]} />
                  <Text style={styles.ageLegendText}>Children (0-12)</Text>
                </View>
                <View style={styles.ageLegendItem}>
                  <View style={[styles.ageLegendColor, { backgroundColor: '#4ECDC4' }]} />
                  <Text style={styles.ageLegendText}>Teens (13-19)</Text>
                </View>
                <View style={styles.ageLegendItem}>
                  <View style={[styles.ageLegendColor, { backgroundColor: '#45B7D1' }]} />
                  <Text style={styles.ageLegendText}>Young Adults (20-34)</Text>
                </View>
                <View style={styles.ageLegendItem}>
                  <View style={[styles.ageLegendColor, { backgroundColor: '#96CEB4' }]} />
                  <Text style={styles.ageLegendText}>Adults (35-54)</Text>
                </View>
                <View style={styles.ageLegendItem}>
                  <View style={[styles.ageLegendColor, { backgroundColor: '#FFEAA7' }]} />
                  <Text style={styles.ageLegendText}>Seniors (55+)</Text>
                </View>
              </View>
            </View>
          )}

          {familyMembers.length > 0 ? (
            <FlatList
              data={familyMembers}
              renderItem={renderFamilyMember}
              keyExtractor={(item) => item._id || item.id}
              scrollEnabled={false}
            />
          ) : (
            <View style={styles.emptyContainer}>
              <Ionicons name="people-outline" size={48} color={colors.textSecondary} />
              <Text style={styles.emptyText}>No family members added</Text>
              <Text style={styles.emptySubtext}>
                Add family members to customize meal plans for everyone
              </Text>
            </View>
          )}
        </View>

        {/* User Dietary Preferences Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Your Dietary Preferences</Text>
            <TouchableOpacity
              style={styles.editButton}
              onPress={() => setShowEditPreferencesModal(true)}
            >
              <Ionicons name="pencil" size={20} color={colors.primary} />
              <Text style={styles.editButtonText}>Edit</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.preferencesCard}>
            <View style={styles.preferenceRow}>
              <Text style={styles.preferenceLabel}>Restrictions:</Text>
              <Text style={styles.preferenceValue}>
                {userDietaryPreferences.restrictions?.length > 0
                  ? userDietaryPreferences.restrictions.join(', ')
                  : 'None'
                }
              </Text>
            </View>

            <View style={styles.preferenceRow}>
              <Text style={styles.preferenceLabel}>Allergies:</Text>
              <Text style={styles.preferenceValue}>
                {userDietaryPreferences.allergies?.length > 0
                  ? userDietaryPreferences.allergies.join(', ')
                  : 'None'
                }
              </Text>
            </View>

            {userDietaryPreferences.calorieTarget && (
              <View style={styles.preferenceRow}>
                <Text style={styles.preferenceLabel}>Daily Calorie Target:</Text>
                <Text style={styles.preferenceValue}>
                  {userDietaryPreferences.calorieTarget} calories
                </Text>
              </View>
            )}
          </View>
        </View>
      </ScrollView>

      {/* Add Member Modal */}
      <Modal
        visible={showAddMemberModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowAddMemberModal(false)}>
              <Text style={styles.modalCancelText}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Add Family Member</Text>
            <TouchableOpacity onPress={handleAddMember}>
              <Text style={styles.modalSaveText}>Save</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Name</Text>
              <TextInput
                style={styles.textInput}
                value={newMember.name}
                onChangeText={(text) => setNewMember(prev => ({ ...prev, name: text }))}
                placeholder="Enter family member's name"
                placeholderTextColor={colors.textSecondary}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Date of Birth (Optional)</Text>
              <TextInput
                style={styles.textInput}
                value={newMember.dateOfBirth}
                onChangeText={(text) => setNewMember(prev => ({ ...prev, dateOfBirth: text }))}
                placeholder="YYYY-MM-DD (e.g., 1990-05-15)"
                placeholderTextColor={colors.textSecondary}
              />
              <Text style={styles.inputHint}>
                Enter date in YYYY-MM-DD format to calculate age
              </Text>
            </View>

            <View style={styles.inputGroup}>
              <View style={styles.requiredFieldHeader}>
                <Text style={styles.inputLabel}>Dietary Preferences</Text>
                <Text style={styles.requiredIndicator}>* Required</Text>
              </View>
              <Text style={styles.inputSubtitle}>Select at least one dietary restriction or allergy</Text>

              <Text style={styles.subSectionLabel}>Dietary Restrictions</Text>
              <View style={styles.optionsContainer}>
                {dietaryOptions.map(option =>
                  renderOptionButton(
                    option,
                    newMember.dietaryPreferences.restrictions.includes(option),
                    () => toggleNewMemberRestriction(option),
                    colors.primary
                  )
                )}
                {/* Other option button for restrictions */}
                <TouchableOpacity
                  style={[
                    styles.optionButton,
                    (showCustomRestrictionInputNewMember || newMember.dietaryPreferences.restrictions.filter(restriction => !dietaryOptions.includes(restriction)).length > 0) && { backgroundColor: colors.primary, borderColor: colors.primary }
                  ]}
                  onPress={() => setShowCustomRestrictionInputNewMember(!showCustomRestrictionInputNewMember)}
                >
                  <Text style={[
                    styles.optionText,
                    (showCustomRestrictionInputNewMember || newMember.dietaryPreferences.restrictions.filter(restriction => !dietaryOptions.includes(restriction)).length > 0) && styles.optionTextSelected
                  ]}>
                    Other
                  </Text>
                </TouchableOpacity>
              </View>

              {/* Custom restriction input */}
              {showCustomRestrictionInputNewMember && (
                <View style={styles.customInputContainer}>
                  <TextInput
                    style={styles.customInput}
                    placeholder="Enter custom dietary restriction"
                    value={customRestrictionNewMember}
                    onChangeText={setCustomRestrictionNewMember}
                    placeholderTextColor={colors.textSecondary}
                    onSubmitEditing={addCustomRestrictionNewMember}
                    returnKeyType="done"
                  />
                  <TouchableOpacity
                    style={styles.customAddButton}
                    onPress={addCustomRestrictionNewMember}
                    disabled={!customRestrictionNewMember.trim()}
                  >
                    <Text style={[
                      styles.customAddButtonText,
                      !customRestrictionNewMember.trim() && styles.customAddButtonTextDisabled
                    ]}>
                      Add
                    </Text>
                  </TouchableOpacity>
                </View>
              )}

              {/* Display custom restrictions */}
              {newMember.dietaryPreferences.restrictions.filter(restriction => !dietaryOptions.includes(restriction)).length > 0 && (
                <View style={styles.customItemsContainer}>
                  <Text style={styles.customItemsTitle}>Custom Dietary Restrictions:</Text>
                  <View style={styles.customItemsList}>
                    {newMember.dietaryPreferences.restrictions
                      .filter(restriction => !dietaryOptions.includes(restriction))
                      .map(restriction => (
                        <View key={restriction} style={styles.customRestrictionItem}>
                          <Text style={styles.customRestrictionText}>{restriction}</Text>
                          <TouchableOpacity
                            style={styles.removeCustomRestrictionButton}
                            onPress={() => removeCustomRestrictionNewMember(restriction)}
                          >
                            <Ionicons name="close-circle" size={20} color={colors.error} />
                          </TouchableOpacity>
                        </View>
                      ))}
                  </View>
                </View>
              )}

              <Text style={styles.subSectionLabel}>Allergies</Text>
              <View style={styles.optionsContainer}>
                {allergyOptions.map(option =>
                  renderOptionButton(
                    option,
                    newMember.dietaryPreferences.allergies.includes(option),
                    () => toggleNewMemberAllergy(option),
                    colors.secondary
                  )
                )}
                {/* Other option button */}
                <TouchableOpacity
                  style={[
                    styles.optionButton,
                    (showCustomAllergyInputNewMember || newMember.dietaryPreferences.allergies.filter(allergy => !allergyOptions.includes(allergy)).length > 0) && { backgroundColor: colors.secondary, borderColor: colors.secondary }
                  ]}
                  onPress={() => setShowCustomAllergyInputNewMember(!showCustomAllergyInputNewMember)}
                >
                  <Text style={[
                    styles.optionText,
                    (showCustomAllergyInputNewMember || newMember.dietaryPreferences.allergies.filter(allergy => !allergyOptions.includes(allergy)).length > 0) && styles.optionTextSelected
                  ]}>
                    Other
                  </Text>
                </TouchableOpacity>
              </View>

              {/* Custom allergy input for new member */}
              {showCustomAllergyInputNewMember && (
                <View style={styles.customInputContainer}>
                  <TextInput
                    style={styles.customInput}
                    placeholder="Enter custom allergy"
                    value={customAllergyNewMember}
                    onChangeText={setCustomAllergyNewMember}
                    placeholderTextColor={colors.textSecondary}
                    onSubmitEditing={addCustomAllergyNewMember}
                    returnKeyType="done"
                  />
                  <TouchableOpacity
                    style={styles.customAddButton}
                    onPress={addCustomAllergyNewMember}
                    disabled={!customAllergyNewMember.trim()}
                  >
                    <Text style={[
                      styles.customAddButtonText,
                      !customAllergyNewMember.trim() && styles.customAddButtonTextDisabled
                    ]}>
                      Add
                    </Text>
                  </TouchableOpacity>
                </View>
              )}

              {/* Display custom allergies for new member */}
              {newMember.dietaryPreferences.allergies.filter(allergy => !allergyOptions.includes(allergy)).length > 0 && (
                <View style={styles.customAllergiesContainer}>
                  <Text style={styles.customAllergiesTitle}>Custom Allergies:</Text>
                  <View style={styles.customAllergiesList}>
                    {newMember.dietaryPreferences.allergies
                      .filter(allergy => !allergyOptions.includes(allergy))
                      .map(allergy => (
                        <View key={allergy} style={styles.customAllergyItem}>
                          <Text style={styles.customAllergyText}>{allergy}</Text>
                          <TouchableOpacity
                            style={styles.removeCustomAllergyButton}
                            onPress={() => removeCustomAllergyNewMember(allergy)}
                          >
                            <Ionicons name="close-circle" size={20} color={colors.error} />
                          </TouchableOpacity>
                        </View>
                      ))}
                  </View>
                </View>
              )}
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Daily Calorie Target</Text>
              <TextInput
                style={styles.textInput}
                value={newMember.dietaryPreferences.calorieTarget}
                onChangeText={(text) => setNewMember(prev => ({
                  ...prev,
                  dietaryPreferences: {
                    ...prev.dietaryPreferences,
                    calorieTarget: text
                  }
                }))}
                placeholder="e.g., 2000"
                placeholderTextColor={colors.textSecondary}
                keyboardType="numeric"
              />
            </View>
          </ScrollView>
        </SafeAreaView>
      </Modal>

      {/* Edit User Preferences Modal */}
      <Modal
        visible={showEditPreferencesModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => {
              setShowEditPreferencesModal(false);
              setConflicts(null);
            }}>
              <Text style={styles.modalCancelText}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Edit Your Preferences</Text>
            <TouchableOpacity onPress={handleUpdateUserPreferences}>
              <Text style={styles.modalSaveText}>Save</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Dietary Restrictions</Text>
              <Text style={styles.inputSubtitle}>Select all that apply to you</Text>
              <View style={styles.optionsContainer}>
                {dietaryOptions.map(option =>
                  renderOptionButton(
                    option,
                    (userDietaryPreferences.restrictions || []).includes(option),
                    () => toggleUserRestriction(option),
                    colors.primary
                  )
                )}
                {/* Other option button for user restrictions */}
                <TouchableOpacity
                  style={[
                    styles.optionButton,
                    (showCustomRestrictionInputUser || (userDietaryPreferences.restrictions || []).filter(restriction => !dietaryOptions.includes(restriction)).length > 0) && { backgroundColor: colors.primary, borderColor: colors.primary }
                  ]}
                  onPress={() => setShowCustomRestrictionInputUser(!showCustomRestrictionInputUser)}
                >
                  <Text style={[
                    styles.optionText,
                    (showCustomRestrictionInputUser || (userDietaryPreferences.restrictions || []).filter(restriction => !dietaryOptions.includes(restriction)).length > 0) && styles.optionTextSelected
                  ]}>
                    Other
                  </Text>
                </TouchableOpacity>
              </View>

              {/* Custom restriction input for user */}
              {showCustomRestrictionInputUser && (
                <View style={styles.customInputContainer}>
                  <TextInput
                    style={styles.customInput}
                    placeholder="Enter custom dietary restriction"
                    value={customRestrictionUser}
                    onChangeText={setCustomRestrictionUser}
                    placeholderTextColor={colors.textSecondary}
                    onSubmitEditing={addCustomRestrictionUser}
                    returnKeyType="done"
                  />
                  <TouchableOpacity
                    style={styles.customAddButton}
                    onPress={addCustomRestrictionUser}
                    disabled={!customRestrictionUser.trim()}
                  >
                    <Text style={[
                      styles.customAddButtonText,
                      !customRestrictionUser.trim() && styles.customAddButtonTextDisabled
                    ]}>
                      Add
                    </Text>
                  </TouchableOpacity>
                </View>
              )}

              {/* Display custom restrictions for user */}
              {(userDietaryPreferences.restrictions || []).filter(restriction => !dietaryOptions.includes(restriction)).length > 0 && (
                <View style={styles.customItemsContainer}>
                  <Text style={styles.customItemsTitle}>Custom Dietary Restrictions:</Text>
                  <View style={styles.customItemsList}>
                    {(userDietaryPreferences.restrictions || [])
                      .filter(restriction => !dietaryOptions.includes(restriction))
                      .map(restriction => (
                        <View key={restriction} style={styles.customRestrictionItem}>
                          <Text style={styles.customRestrictionText}>{restriction}</Text>
                          <TouchableOpacity
                            style={styles.removeCustomRestrictionButton}
                            onPress={() => removeCustomRestrictionUser(restriction)}
                          >
                            <Ionicons name="close-circle" size={20} color={colors.error} />
                          </TouchableOpacity>
                        </View>
                      ))}
                  </View>
                </View>
              )}
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Allergies</Text>
              <Text style={styles.inputSubtitle}>Select any food allergies you have</Text>
              <View style={styles.optionsContainer}>
                {allergyOptions.map(option =>
                  renderOptionButton(
                    option,
                    (userDietaryPreferences.allergies || []).includes(option),
                    () => toggleUserAllergy(option),
                    colors.secondary
                  )
                )}
                {/* Other option button for user */}
                <TouchableOpacity
                  style={[
                    styles.optionButton,
                    (showCustomAllergyInputUser || (userDietaryPreferences.allergies || []).filter(allergy => !allergyOptions.includes(allergy)).length > 0) && { backgroundColor: colors.secondary, borderColor: colors.secondary }
                  ]}
                  onPress={() => setShowCustomAllergyInputUser(!showCustomAllergyInputUser)}
                >
                  <Text style={[
                    styles.optionText,
                    (showCustomAllergyInputUser || (userDietaryPreferences.allergies || []).filter(allergy => !allergyOptions.includes(allergy)).length > 0) && styles.optionTextSelected
                  ]}>
                    Other
                  </Text>
                </TouchableOpacity>
              </View>

              {/* Custom allergy input for user */}
              {showCustomAllergyInputUser && (
                <View style={styles.customInputContainer}>
                  <TextInput
                    style={styles.customInput}
                    placeholder="Enter custom allergy"
                    value={customAllergyUser}
                    onChangeText={setCustomAllergyUser}
                    placeholderTextColor={colors.textSecondary}
                    onSubmitEditing={addCustomAllergyUser}
                    returnKeyType="done"
                  />
                  <TouchableOpacity
                    style={styles.customAddButton}
                    onPress={addCustomAllergyUser}
                    disabled={!customAllergyUser.trim()}
                  >
                    <Text style={[
                      styles.customAddButtonText,
                      !customAllergyUser.trim() && styles.customAddButtonTextDisabled
                    ]}>
                      Add
                    </Text>
                  </TouchableOpacity>
                </View>
              )}

              {/* Display custom allergies for user */}
              {(userDietaryPreferences.allergies || []).filter(allergy => !allergyOptions.includes(allergy)).length > 0 && (
                <View style={styles.customAllergiesContainer}>
                  <Text style={styles.customAllergiesTitle}>Custom Allergies:</Text>
                  <View style={styles.customAllergiesList}>
                    {(userDietaryPreferences.allergies || [])
                      .filter(allergy => !allergyOptions.includes(allergy))
                      .map(allergy => (
                        <View key={allergy} style={styles.customAllergyItem}>
                          <Text style={styles.customAllergyText}>{allergy}</Text>
                          <TouchableOpacity
                            style={styles.removeCustomAllergyButton}
                            onPress={() => removeCustomAllergyUser(allergy)}
                          >
                            <Ionicons name="close-circle" size={20} color={colors.error} />
                          </TouchableOpacity>
                        </View>
                      ))}
                  </View>
                </View>
              )}
            </View>

            {/* AI Conflict Detection Warning */}
            {conflicts && conflicts.hasConflicts && (
              <View style={styles.conflictSection}>
                <View style={styles.conflictHeader}>
                  <Ionicons name="warning" size={20} color={colors.warning} />
                  <Text style={styles.conflictTitle}>Dietary Conflicts Detected</Text>
                </View>
                {conflicts.conflicts.map((conflict, index) => (
                  <View key={index} style={styles.conflictItem}>
                    <Text style={styles.conflictText}>
                      <Text style={styles.conflictItems}>
                        {conflict.items.join(' + ')}
                      </Text>
                      {conflict.type === 'family' && (
                        <Text style={styles.conflictType}> (Family Conflict)</Text>
                      )}
                      {conflict.type === 'user-family' && (
                        <Text style={styles.conflictType}> (User vs Family)</Text>
                      )}
                      : {conflict.reason}
                    </Text>
                    {conflict.severity === 'high' && (
                      <View style={styles.severityBadge}>
                        <Text style={styles.severityText}>High</Text>
                      </View>
                    )}
                  </View>
                ))}
                {conflicts.suggestions && conflicts.suggestions.length > 0 && (
                  <View style={styles.suggestionsContainer}>
                    <Text style={styles.suggestionsTitle}>AI Suggestions:</Text>
                    {conflicts.suggestions.map((suggestion, index) => (
                      <Text key={index} style={styles.suggestionText}>
                        • {suggestion}
                      </Text>
                    ))}
                  </View>
                )}
                <Text style={styles.conflictNote}>
                  Note: You can still save these preferences, but consider the conflicts above.
                </Text>
              </View>
            )}

            {/* Loading indicator for conflict checking */}
            {checkingConflicts && (
              <View style={styles.checkingSection}>
                <ActivityIndicator size="small" color={colors.primary} />
                <Text style={styles.checkingText}>Checking for dietary conflicts...</Text>
              </View>
            )}

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Daily Calorie Target</Text>
              <TextInput
                style={styles.textInput}
                value={userDietaryPreferences.calorieTarget?.toString() || ''}
                onChangeText={(text) => setUserDietaryPreferences(prev => ({
                  ...prev,
                  calorieTarget: text
                }))}
                placeholder="e.g., 2000"
                placeholderTextColor={colors.textSecondary}
                keyboardType="numeric"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Meal Frequency</Text>
              <Text style={styles.inputSubtitle}>How many meals do you prefer per day?</Text>
              <View style={styles.frequencyContainer}>
                {[2, 3, 4, 5, 6].map(frequency => (
                  <TouchableOpacity
                    key={frequency}
                    style={[
                      styles.frequencyButton,
                      userDietaryPreferences.mealFrequency === frequency && styles.frequencyButtonSelected
                    ]}
                    onPress={() => setUserDietaryPreferences(prev => ({ ...prev, mealFrequency: frequency }))}
                  >
                    <Text style={[
                      styles.frequencyText,
                      userDietaryPreferences.mealFrequency === frequency && styles.frequencyTextSelected
                    ]}>
                      {frequency}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  header: {
    backgroundColor: colors.primary,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTitle: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.surface,
  },
  addButton: {
    backgroundColor: colors.primary,
    padding: spacing.sm,
    borderRadius: borderRadius.medium,
  },
  section: {
    backgroundColor: colors.surface,
    marginBottom: spacing.md,
    padding: spacing.md,
  },
  sectionTitle: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing.md,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.medium,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  editButtonText: {
    fontSize: fonts.sizes.small,
    color: colors.primary,
    marginLeft: spacing.xs,
    fontWeight: '500',
  },
  memberCard: {
    backgroundColor: colors.background,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
    marginBottom: spacing.md,
    ...commonStyles.shadowSmall,
  },
  memberHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  memberAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  memberAvatarText: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.surface,
  },
  memberInfo: {
    flex: 1,
    marginLeft: spacing.md,
  },
  memberName: {
    fontSize: fonts.sizes.medium,
    fontWeight: 'bold',
    color: colors.text,
  },
  memberAge: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  memberPreferences: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  removeButton: {
    padding: spacing.sm,
  },
  allergiesSection: {
    marginTop: spacing.md,
    paddingTop: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  allergiesLabel: {
    fontSize: fonts.sizes.small,
    fontWeight: 'bold',
    color: colors.text,
  },
  allergiesText: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  preferencesCard: {
    backgroundColor: colors.background,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
  },
  preferenceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  preferenceLabel: {
    fontSize: fonts.sizes.medium,
    fontWeight: '500',
    color: colors.text,
    flex: 1,
  },
  preferenceValue: {
    fontSize: fonts.sizes.medium,
    color: colors.textSecondary,
    flex: 2,
    textAlign: 'right',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  emptyText: {
    fontSize: fonts.sizes.medium,
    fontWeight: '500',
    color: colors.textSecondary,
    marginTop: spacing.md,
  },
  emptySubtext: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginTop: spacing.xs,
    textAlign: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: colors.background,
  },
  modalHeader: {
    backgroundColor: colors.surface,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  modalTitle: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.text,
  },
  modalCancelText: {
    fontSize: fonts.sizes.medium,
    color: colors.textSecondary,
  },
  modalSaveText: {
    fontSize: fonts.sizes.medium,
    color: colors.primary,
    fontWeight: 'bold',
  },
  modalContent: {
    flex: 1,
    padding: spacing.md,
  },
  inputGroup: {
    marginBottom: spacing.lg,
  },
  inputLabel: {
    fontSize: fonts.sizes.medium,
    fontWeight: '500',
    color: colors.text,
    marginBottom: spacing.sm,
  },
  inputSubtitle: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginBottom: spacing.md,
  },
  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.medium,
    borderWidth: 1,
    borderColor: colors.border,
    marginBottom: spacing.sm,
  },
  optionText: {
    fontSize: fonts.sizes.medium,
    color: colors.text,
    fontWeight: '500',
  },
  optionTextSelected: {
    color: colors.surface,
  },
  checkmark: {
    marginLeft: spacing.xs,
  },
  frequencyContainer: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  frequencyButton: {
    flex: 1,
    backgroundColor: colors.background,
    paddingVertical: spacing.md,
    borderRadius: borderRadius.medium,
    borderWidth: 1,
    borderColor: colors.border,
    alignItems: 'center',
  },
  frequencyButtonSelected: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  frequencyText: {
    fontSize: fonts.sizes.medium,
    color: colors.text,
    fontWeight: '600',
  },
  frequencyTextSelected: {
    color: colors.surface,
  },
  // Conflict detection styles
  conflictSection: {
    backgroundColor: '#FFF3CD',
    borderColor: colors.warning,
    borderWidth: 1,
    borderRadius: borderRadius.medium,
    marginBottom: spacing.lg,
    padding: spacing.md,
  },
  conflictHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  conflictTitle: {
    fontSize: fonts.sizes.medium,
    fontWeight: 'bold',
    color: colors.warning,
    marginLeft: spacing.xs,
  },
  conflictItem: {
    marginBottom: spacing.sm,
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
  },
  conflictText: {
    fontSize: fonts.sizes.small,
    color: colors.text,
    flex: 1,
  },
  conflictItems: {
    fontWeight: 'bold',
    color: colors.warning,
  },
  conflictType: {
    fontWeight: 'bold',
    color: colors.secondary,
    fontSize: fonts.sizes.small,
  },
  severityBadge: {
    backgroundColor: colors.error,
    paddingHorizontal: spacing.xs,
    paddingVertical: 2,
    borderRadius: borderRadius.small,
    marginLeft: spacing.xs,
  },
  severityText: {
    fontSize: fonts.sizes.small,
    color: colors.surface,
    fontWeight: 'bold',
  },
  suggestionsContainer: {
    marginTop: spacing.sm,
    paddingTop: spacing.sm,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  suggestionsTitle: {
    fontSize: fonts.sizes.small,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing.xs,
  },
  suggestionText: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  conflictNote: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    fontStyle: 'italic',
    marginTop: spacing.sm,
  },
  checkingSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.surface,
    marginBottom: spacing.lg,
    padding: spacing.md,
    borderRadius: borderRadius.medium,
  },
  checkingText: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginLeft: spacing.sm,
  },
  inputHint: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginTop: spacing.xs,
    fontStyle: 'italic',
  },
  textInput: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.medium,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    fontSize: fonts.sizes.medium,
    color: colors.text,
    borderWidth: 1,
    borderColor: colors.border,
  },
  pickerContainer: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.medium,
    borderWidth: 1,
    borderColor: colors.border,
  },
  picker: {
    height: 50,
  },
  requiredFieldHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  requiredIndicator: {
    fontSize: fonts.sizes.small,
    color: colors.error,
    fontWeight: '600',
  },
  subSectionLabel: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.text,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  sectionHeaderWithLegend: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  ageLegendButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.small,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  ageLegendButtonText: {
    fontSize: fonts.sizes.small,
    color: colors.primary,
    marginLeft: spacing.xs,
    fontWeight: '500',
  },
  ageLegend: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
    marginBottom: spacing.md,
  },
  ageLegendTitle: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing.sm,
  },
  ageLegendItems: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  ageLegendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
    minWidth: '45%',
  },
  ageLegendColor: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: spacing.sm,
  },
  ageLegendText: {
    fontSize: fonts.sizes.small,
    color: colors.text,
  },
  customInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: spacing.md,
    gap: spacing.sm,
  },
  customInput: {
    flex: 1,
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: borderRadius.medium,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    fontSize: fonts.sizes.medium,
    color: colors.text,
  },
  customAddButton: {
    backgroundColor: colors.secondary,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.medium,
  },
  customAddButtonText: {
    color: colors.surface,
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
  },
  customAddButtonTextDisabled: {
    opacity: 0.5,
  },
  customAllergiesContainer: {
    marginTop: spacing.md,
    padding: spacing.md,
    backgroundColor: colors.backgroundSecondary,
    borderRadius: borderRadius.medium,
  },
  customAllergiesTitle: {
    fontSize: fonts.sizes.small,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing.sm,
  },
  customAllergiesList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  customAllergyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.small,
    borderWidth: 1,
    borderColor: colors.secondary,
  },
  customAllergyText: {
    fontSize: fonts.sizes.small,
    color: colors.text,
    marginRight: spacing.xs,
  },
  removeCustomAllergyButton: {
    padding: 2,
  },
  // Custom restriction styles (reusing allergy styles structure)
  customItemsContainer: {
    marginTop: spacing.md,
    padding: spacing.md,
    backgroundColor: colors.backgroundSecondary,
    borderRadius: borderRadius.medium,
  },
  customItemsTitle: {
    fontSize: fonts.sizes.small,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing.sm,
  },
  customItemsList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  customRestrictionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.small,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  customRestrictionText: {
    fontSize: fonts.sizes.small,
    color: colors.text,
    marginRight: spacing.xs,
  },
  removeCustomRestrictionButton: {
    padding: 2,
  },
});

export default FamilyScreen;
